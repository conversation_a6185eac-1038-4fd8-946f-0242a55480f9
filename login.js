import { chromium } from 'playwright';
import fs from 'fs';
import config from './config.js';

async function login() {
  console.log('启动浏览器...');
  const browser = await chromium.launch(config.browserOptions);
  const context = await browser.newContext();

  if (fs.existsSync(config.cookieFile)) {
    // 读取并设置cookies
    const cookies = JSON.parse(fs.readFileSync(config.cookieFile, 'utf8'));
    await context.addCookies(cookies);
    console.log(`已加载 ${cookies.length} 个cookies`);
  }


  const page = await context.newPage();

  try {
    console.log('导航到登录页面...');
    // 首先访问主页面，通常会重定向到登录页面
    await page.goto(config.webideUrl);

    // 等待页面加载
    await page.waitForTimeout(config.waitTimes.pageLoad);

    console.log('当前页面URL:', page.url());
    console.log('页面标题:', await page.title());

    // 检查是否已经登录（如果页面包含编辑器元素，说明已登录）
    const isLoggedIn = await page.locator('.monaco-grid-view').count() > 0;

    if (isLoggedIn) {
      console.log('检测到已经登录状态，保存cookie...');
    } else {
      console.log('需要登录，请在浏览器中手动完成登录过程...');
      console.log('登录完成后，请按 Enter 键继续...');

      // 等待用户手动登录
      await waitForUserInput();

      // 等待登录完成，检查是否出现编辑器界面
      console.log('等待登录完成...');
      try {
        await page.waitForSelector('.monaco-grid-view', {
          timeout: 60000
        });

      } catch (error) {
        console.log('未检测到编辑器界面，但继续保存cookie...');
      }
    }

    // 保存cookies
    const cookies = await context.cookies();
    fs.writeFileSync(config.cookieFile, JSON.stringify(cookies, null, 2));
    console.log(`Cookies已保存到 ${config.cookieFile}`);
    console.log(`保存了 ${cookies.length} 个cookies`);

    // 显示保存的cookie信息（仅显示名称，不显示值）
    console.log('保存的cookie名称:');
    cookies.forEach(cookie => {
      console.log(`  - ${cookie.name} (域名: ${cookie.domain})`);
    });

  } catch (error) {
    console.error('登录过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 等待用户输入的辅助函数
async function waitForUserInput() {
  const { default: readline } = await import('readline');
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('', () => {
      rl.close();
      resolve();
    });
  });
}

login().catch(console.error);